import cv2
import numpy as np
import os
import math
from google.cloud import vision

def detect_phone_photo_characteristics(image):
    """Detect if image is likely taken with a phone camera"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Calculate noise level using Laplacian variance
    laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
    
    # Calculate blur level using gradient magnitude
    sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    gradient_magnitude = np.sqrt(sobelx**2 + sobely**2)
    blur_level = gradient_magnitude.mean()
    
    # Calculate brightness distribution
    brightness_std = gray.std()
    
    # Phone photo characteristics
    is_phone_photo = (
        laplacian_var < 500 or  # High noise/compression
        blur_level < 20 or      # Motion blur or focus issues
        brightness_std < 30     # Poor dynamic range
    )
    
    return is_phone_photo, {
        'noise_level': laplacian_var,
        'blur_level': blur_level,
        'brightness_std': brightness_std
    }

def preprocess_phone_photo(image, photo_stats):
    """Apply specific preprocessing for phone photos"""
    # Noise reduction
    denoised = cv2.bilateralFilter(image, 9, 80, 80)
    
    # Sharpening if image is blurry
    if photo_stats['blur_level'] < 15:
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
    else:
        sharpened = denoised
    
    # Color enhancement for phone photos
    lab = cv2.cvtColor(sharpened, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    
    # More aggressive CLAHE for L channel
    clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(6,6))
    l = clahe.apply(l)
    
    # Enhance color channels more aggressively
    a = cv2.convertScaleAbs(a, alpha=1.4, beta=15)
    b = cv2.convertScaleAbs(b, alpha=1.4, beta=15)
    
    # Merge and convert back
    enhanced_lab = cv2.merge([l, a, b])
    enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
    
    # Contrast enhancement
    enhanced = cv2.convertScaleAbs(enhanced, alpha=1.3, beta=10)
    
    return enhanced

def color_based_enhancement(image):
    """Enhance based on color differences"""
    # Convert to LAB color space for better color separation
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)

    # Apply CLAHE to L channel
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    l = clahe.apply(l)

    # Enhance A and B channels
    a = cv2.convertScaleAbs(a, alpha=1.2, beta=10)
    b = cv2.convertScaleAbs(b, alpha=1.2, beta=10)

    # Merge and convert back
    enhanced_lab = cv2.merge([l, a, b])
    enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)

    return enhanced

def detect_cards_color_separation(image, min_area_percentage=4.0, debugging=False):
    """Detect cards using color separation method optimized for phone photos"""
    if debugging:
        print(f"Starting card detection with min area: {min_area_percentage}%")
    
    # Calculate minimum area in pixels from percentage
    total_area = image.shape[0] * image.shape[1]
    min_area = (min_area_percentage / 100.0) * total_area
    
    if debugging:
        print(f"Image size: {image.shape[1]}x{image.shape[0]}, Total area: {total_area}, Min area: {min_area}")

    # Detect if this is a phone photo
    is_phone_photo, photo_stats = detect_phone_photo_characteristics(image)
    
    if debugging:
        print(f"Phone photo detected: {is_phone_photo}")
        if is_phone_photo:
            print(f"Photo stats: {photo_stats}")
    
    if is_phone_photo:
        # Apply phone photo specific preprocessing
        processed_image = preprocess_phone_photo(image, photo_stats)
    else:
        # Apply standard color enhancement
        processed_image = color_based_enhancement(image)

    # Convert to LAB and HSV color spaces
    lab = cv2.cvtColor(processed_image, cv2.COLOR_BGR2LAB)
    hsv = cv2.cvtColor(processed_image, cv2.COLOR_BGR2HSV)

    # For phone photos, try multiple channels and pick the best one
    if is_phone_photo:
        # Try L channel from LAB
        l_channel = lab[:,:,0]
        # Try V channel from HSV
        v_channel = hsv[:,:,2]
        # Try grayscale
        gray_channel = cv2.cvtColor(processed_image, cv2.COLOR_BGR2GRAY)
        
        # Calculate contrast for each channel
        l_contrast = l_channel.std()
        v_contrast = v_channel.std()
        gray_contrast = gray_channel.std()
        
        # Pick the channel with highest contrast
        if l_contrast >= v_contrast and l_contrast >= gray_contrast:
            working_channel = l_channel
            channel_name = "LAB L-Channel"
        elif v_contrast >= gray_contrast:
            working_channel = v_channel
            channel_name = "HSV V-Channel"
        else:
            working_channel = gray_channel
            channel_name = "Grayscale"
            
        if debugging:
            print(f"Selected {channel_name} (contrast: {working_channel.std():.1f})")
    else:
        # Use L channel from LAB for regular photos
        working_channel = lab[:,:,0]

    # Adaptive histogram equalization with phone photo adjustments
    if is_phone_photo:
        clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(6,6))
    else:
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        
    equalized = clahe.apply(working_channel)

    # Edge detection with phone photo adjustments
    if is_phone_photo:
        # Apply noise reduction before edge detection
        denoised = cv2.bilateralFilter(equalized, 9, 75, 75)
        # Use more sensitive edge detection for phone photos
        edges = cv2.Canny(denoised, 20, 80)
    else:
        edges = cv2.Canny(equalized, 30, 100)

    # Morphological operations with phone photo adjustments
    if is_phone_photo:
        # More aggressive morphological operations for phone photos
        kernel_small = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
        
        # Close small gaps
        closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_small)
        # Dilate to connect nearby edges
        dilated = cv2.dilate(closed, kernel_small, iterations=2)
        # Close larger gaps
        final_edges = cv2.morphologyEx(dilated, cv2.MORPH_CLOSE, kernel_large)
    else:
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        final_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

    # Find contours
    contours, _ = cv2.findContours(final_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    detections = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < min_area:
            continue

        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = min(w, h) / max(w, h)

        if aspect_ratio < 0.3:  # Skip very elongated shapes
            continue

        # Additional filtering for phone photos
        if is_phone_photo:
            # More lenient filtering for phone photos
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                continue
                
            # Check compactness (phone photos may have less perfect rectangles)
            compactness = (perimeter * perimeter) / (4 * np.pi * area)
            if compactness > 8.0:  # More lenient than usual
                continue

        detections.append({
            'bbox': (x, y, w, h),
            'area': area,
            'aspect_ratio': aspect_ratio
        })

    # Sort by area (smallest to largest as requested)
    detections.sort(key=lambda x: x['area'])
    
    if debugging:
        print(f"Found {len(detections)} potential card detections")
        for i, det in enumerate(detections):
            area_pct = (det['area'] / total_area) * 100
            print(f"  Detection {i+1}: {det['bbox']}, area: {area_pct:.1f}%")

    return detections

def rotate_image(image, angle):
    """Rotate image by given angle"""
    if angle == 0:
        return image
    
    # Get image dimensions
    h, w = image.shape[:2]
    center = (w // 2, h // 2)
    
    # Calculate rotation matrix
    rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
    
    # Calculate new dimensions after rotation
    cos_angle = abs(rotation_matrix[0, 0])
    sin_angle = abs(rotation_matrix[0, 1])
    new_w = int((h * sin_angle) + (w * cos_angle))
    new_h = int((h * cos_angle) + (w * sin_angle))
    
    # Adjust rotation matrix for new center
    rotation_matrix[0, 2] += (new_w / 2) - center[0]
    rotation_matrix[1, 2] += (new_h / 2) - center[1]
    
    # Perform rotation
    rotated = cv2.warpAffine(image, rotation_matrix, (new_w, new_h), 
                            flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT, 
                            borderValue=(255, 255, 255))
    
    return rotated

def detect_rotation_and_keyword(image_path, keyword=None, debugging=False, rotation_only=False):
    """Detect rotation and keyword using Google Vision OCR"""
    if debugging:
        print(f"Checking OCR and rotation for: {image_path}")
    
    try:
        # Initialize Google Vision client
        client = vision.ImageAnnotatorClient()
        
        # Read image
        with open(image_path, 'rb') as image_file:
            content = image_file.read()
        
        # Create Vision API image object
        image = vision.Image(content=content)
        
        # Perform document text detection (includes both text and orientation info)
        response = client.document_text_detection(image=image)
        
        if response.error.message:
            if debugging:
                print(f"Google Vision API error: {response.error.message}")
            return False
        
        # Extract detected text
        detected_text = ""
        if response.full_text_annotation:
            detected_text = response.full_text_annotation.text
        
        if debugging:
            print(f"Detected text: {detected_text[:100]}...")
        
        # If rotation_only is True, skip keyword checking
        if not rotation_only:
            # Check for "Kingdom" keyword
            kingdom_found = "kingdom" in detected_text.lower() or "المملكة" in detected_text

            # Check for additional keyword if provided
            keyword_found = True
            if keyword:
                keyword_found = keyword.lower() in detected_text.lower()

            if debugging:
                print(f"Kingdom found: {kingdom_found}, Keyword '{keyword}' found: {keyword_found}")

            if not (kingdom_found and keyword_found):
                return False
        
        # Calculate rotation from text orientation
        rotation_angle = 0
        if response.full_text_annotation and response.full_text_annotation.pages:
            page = response.full_text_annotation.pages[0]
            if page.blocks:
                # Calculate average rotation from text blocks
                total_rotation = 0
                block_count = 0
                
                for block in page.blocks:
                    for paragraph in block.paragraphs:
                        for word in paragraph.words:
                            # Get bounding box vertices
                            vertices = word.bounding_box.vertices
                            if len(vertices) >= 2:
                                # Calculate angle from first two vertices
                                dx = vertices[1].x - vertices[0].x
                                dy = vertices[1].y - vertices[0].y
                                angle = math.degrees(math.atan2(dy, dx))
                                total_rotation += angle
                                block_count += 1
                
                if block_count > 0:
                    avg_rotation = total_rotation / block_count
                    # Normalize to -90, 0, 90 degrees
                    if avg_rotation > 45:
                        rotation_angle = 90
                    elif avg_rotation < -45:
                        rotation_angle = -90
                    else:
                        rotation_angle = 0
        
        if debugging:
            print(f"Detected rotation: {rotation_angle}°")
        
        # If rotation is needed, rotate and save the image
        if rotation_angle != 0:
            image_cv = cv2.imread(image_path)
            rotated_image = rotate_image(image_cv, -rotation_angle)  # Negative to correct rotation
            cv2.imwrite(image_path, rotated_image)
            if debugging:
                print(f"Image rotated by {-rotation_angle}° and saved")

        # If rotation_only is True, always return True (we just wanted to fix rotation)
        if rotation_only:
            return True

        return True
        
    except Exception as e:
        if debugging:
            print(f"Error in OCR detection: {e}")
        return False

def crop_cards(image_path, cards_to_detect=1, min_detection_area=4, debugging=True):
    """
    Main function to detect and crop cards from an image

    Args:
        image_path: Path to the input image
        cards_to_detect: Number of cards to detect (default: 1)
        min_detection_area: Minimum area percentage for card detection (default: 4)
        debugging: Enable debug prints (default: True)

    Returns:
        List of paths to cropped card images
    """
    if debugging:
        print(f"Processing image: {image_path}")
        print(f"Looking for {cards_to_detect} card(s)")
    
    # Create output directory
    output_dir = "Ready Cards"
    os.makedirs(output_dir, exist_ok=True)
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        if debugging:
            print(f"Error: Could not load image {image_path}")
        return []
    
    # Always check for rotation first, regardless of card detection
    if debugging:
        print("Checking image rotation...")

    # Create a temporary copy of the original image for rotation check
    temp_rotation_check = "temp_rotation_check.jpg"
    cv2.imwrite(temp_rotation_check, image)

    # Check if the image needs rotation (this will rotate and save if needed)
    rotation_checked = detect_rotation_and_keyword(temp_rotation_check, debugging=debugging, rotation_only=True)

    # If the image was rotated, reload it
    if rotation_checked:
        image = cv2.imread(temp_rotation_check)
        if debugging:
            print("Image was rotated, reloaded for detection")

    # Clean up temporary file
    if os.path.exists(temp_rotation_check):
        os.remove(temp_rotation_check)

    # Detect cards using color separation
    detections = detect_cards_color_separation(image, min_area_percentage=min_detection_area, debugging=debugging)

    if not detections:
        if debugging:
            print("No cards detected, saving processed image")
        # Save the processed (potentially rotated) image to output directory
        filename = os.path.basename(image_path)
        output_path = os.path.join(output_dir, filename)
        cv2.imwrite(output_path, image)
        return [output_path]
    
    result_paths = []
    cards_found = 0
    
    # Process detections from smallest to largest
    for i, detection in enumerate(detections):
        if cards_found >= cards_to_detect:
            break
            
        x, y, w, h = detection['bbox']
        
        if debugging:
            area_pct = (detection['area'] / (image.shape[0] * image.shape[1])) * 100
            print(f"Processing detection {i+1}: area {area_pct:.1f}%")
        
        # Crop the detected region
        cropped = image[y:y+h, x:x+w]
        
        # Save cropped image temporarily for OCR check
        temp_filename = f"temp_crop_{i}.jpg"
        cv2.imwrite(temp_filename, cropped)
        
        # Check if this crop contains "Kingdom" using OCR
        if detect_rotation_and_keyword(temp_filename, debugging=debugging):
            # This is a valid card, save it to output directory
            filename = os.path.splitext(os.path.basename(image_path))[0]
            output_filename = f"{filename}_card_{cards_found+1}.jpg"
            output_path = os.path.join(output_dir, output_filename)
            
            # Read the potentially rotated image and save to final location
            final_cropped = cv2.imread(temp_filename)
            cv2.imwrite(output_path, final_cropped)
            result_paths.append(output_path)
            cards_found += 1
            
            if debugging:
                print(f"Valid card found and saved: {output_path}")
        else:
            if debugging:
                print(f"Detection {i+1} rejected: no 'Kingdom' keyword found")
        
        # Clean up temporary file
        if os.path.exists(temp_filename):
            os.remove(temp_filename)
    
    if cards_found == 0:
        if debugging:
            print("No valid cards found, saving original image")
        # Save original image to output directory
        filename = os.path.basename(image_path)
        output_path = os.path.join(output_dir, filename)
        cv2.imwrite(output_path, image)
        result_paths.append(output_path)
    
    if debugging:
        print(f"Processing complete. Found {cards_found} valid card(s)")
    
    return result_paths

# Test the function on all test images
if __name__ == "__main__":
    test_images = [f"test images/test{i}.jpg" for i in range(1, 10)]
    
    for test_image in test_images:
        if os.path.exists(test_image):
            print(f"\n{'='*50}")
            print(f"Testing: {test_image}")
            print('='*50)
            
            result_paths = crop_cards(test_image, cards_to_detect=1, min_detection_area=4, debugging=True)
            
            print(f"Results for {test_image}:")
            for path in result_paths:
                print(f"  - {path}")
        else:
            print(f"Test image not found: {test_image}")
