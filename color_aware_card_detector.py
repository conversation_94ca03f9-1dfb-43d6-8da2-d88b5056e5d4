import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import math
import colorsys

class ColorAwareCardDetector:
    def __init__(self, root):
        self.root = root
        self.root.title("Color-Aware Card Detector - Light Green Card Specialist")
        self.root.geometry("1600x1000")
        
        # Variables
        self.image = None
        self.original_image = None
        self.detections = []
        self.current_detection_index = 0
        self.display_image = None
        self.processed_image = None
        self.preprocessing_steps = []
        
        # Target card color (light green) - you can adjust these
        self.target_hue_range = (70, 90)  # Green hue range
        self.target_saturation_range = (20, 80)  # Light saturation
        self.target_value_range = (150, 255)  # Bright value
        
        # Create GUI
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control frame (top)
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File selection
        ttk.Button(control_frame, text="Select Image", command=self.select_image).pack(side=tk.LEFT, padx=(0, 10))
        
        # Quick test buttons for test images
        test_frame = ttk.LabelFrame(control_frame, text="Quick Test")
        test_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        test_buttons_frame = ttk.Frame(test_frame)
        test_buttons_frame.pack(padx=5, pady=2)
        
        for i in range(1, 9):
            ttk.Button(test_buttons_frame, text=f"T{i}", width=3,
                      command=lambda x=i: self.load_test_image(x)).pack(side=tk.LEFT, padx=1)
        
        # Test all button
        ttk.Button(test_frame, text="Test All", command=self.test_all_images).pack(pady=2)
        
        # Detection method selection
        ttk.Label(control_frame, text="Method:").pack(side=tk.LEFT, padx=(10, 5))
        self.method_var = tk.StringVar(value="Color Separation Enhanced")
        method_combo = ttk.Combobox(control_frame, textvariable=self.method_var, width=20, state="readonly")
        method_combo['values'] = ("Color Separation Enhanced", "Phone Image Optimized", "Carpet Background", "Multi-Method")
        method_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection button
        ttk.Button(control_frame, text="Detect Cards", command=self.detect_cards).pack(side=tk.LEFT, padx=(5, 10))
        
        # Navigation buttons
        self.prev_btn = ttk.Button(control_frame, text="Previous", command=self.prev_detection, state=tk.DISABLED)
        self.prev_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.next_btn = ttk.Button(control_frame, text="Next", command=self.next_detection, state=tk.DISABLED)
        self.next_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Detection counter
        self.counter_label = ttk.Label(control_frame, text="No detections")
        self.counter_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # Crop button
        self.crop_btn = ttk.Button(control_frame, text="Crop Current", command=self.crop_current, state=tk.DISABLED)
        self.crop_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # Parameters frame
        params_frame = ttk.LabelFrame(control_frame, text="Color & Detection Parameters")
        params_frame.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Parameters row 1 - Color filtering
        params_row1 = ttk.Frame(params_frame)
        params_row1.pack(padx=5, pady=2)
        
        self.use_color_filter_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_row1, text="Color Filter", variable=self.use_color_filter_var).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(params_row1, text="Min Area:").pack(side=tk.LEFT, padx=(5, 2))
        self.min_area_var = tk.StringVar(value="3000")
        ttk.Entry(params_row1, textvariable=self.min_area_var, width=8).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(params_row1, text="Aspect:").pack(side=tk.LEFT, padx=(5, 2))
        self.aspect_ratio_var = tk.StringVar(value="0.4")
        ttk.Entry(params_row1, textvariable=self.aspect_ratio_var, width=6).pack(side=tk.LEFT)
        
        # Parameters row 2 - Enhancement
        params_row2 = ttk.Frame(params_frame)
        params_row2.pack(padx=5, pady=2)
        
        ttk.Label(params_row2, text="Contrast:").pack(side=tk.LEFT, padx=(0, 2))
        self.contrast_var = tk.StringVar(value="2.5")
        ttk.Entry(params_row2, textvariable=self.contrast_var, width=6).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Label(params_row2, text="Color Thresh:").pack(side=tk.LEFT, padx=(5, 2))
        self.color_threshold_var = tk.StringVar(value="0.3")
        ttk.Entry(params_row2, textvariable=self.color_threshold_var, width=6).pack(side=tk.LEFT)
        
        # Parameters row 3 - Display options
        params_row3 = ttk.Frame(params_frame)
        params_row3.pack(padx=5, pady=2)
        
        self.show_preprocessing_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(params_row3, text="Show Steps", 
                       variable=self.show_preprocessing_var,
                       command=self.update_display).pack(side=tk.LEFT, padx=(0, 5))
        
        self.show_color_mask_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(params_row3, text="Show Color Mask", 
                       variable=self.show_color_mask_var,
                       command=self.update_display).pack(side=tk.LEFT, padx=(0, 5))
        
        self.show_all_detections_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(params_row3, text="Show All", 
                       variable=self.show_all_detections_var,
                       command=self.update_display).pack(side=tk.LEFT)
        
        # Content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Image display frame (left)
        image_frame = ttk.LabelFrame(content_frame, text="Image Display")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Canvas for image display
        self.canvas = tk.Canvas(image_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Info frame (right)
        info_frame = ttk.LabelFrame(content_frame, text="Detection & Color Info")
        info_frame.pack(side=tk.RIGHT, fill=tk.Y)
        info_frame.configure(width=450)
        info_frame.pack_propagate(False)
        
        # Color display frame
        color_display_frame = ttk.Frame(info_frame)
        color_display_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(color_display_frame, text="Detected Card Color:").pack()
        self.color_canvas = tk.Canvas(color_display_frame, height=50, bg="white")
        self.color_canvas.pack(fill=tk.X, pady=2)
        
        self.color_info_label = ttk.Label(color_display_frame, text="No color detected")
        self.color_info_label.pack()
        
        # Info text widget
        self.info_text = tk.Text(info_frame, width=50, height=35, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def rgb_to_hex(self, r, g, b):
        """Convert RGB to hex color code"""
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def get_dominant_color_in_region(self, image, contour):
        """Get the dominant color inside a contour region"""
        # Create mask for the contour
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        cv2.fillPoly(mask, [contour], 255)
        
        # Get pixels inside the contour
        masked_image = cv2.bitwise_and(image, image, mask=mask)
        pixels = masked_image[mask > 0]
        
        if len(pixels) == 0:
            return None, None
        
        # Calculate mean color
        mean_color = np.mean(pixels, axis=0)
        b, g, r = mean_color.astype(int)
        
        # Convert to HSV for better analysis
        hsv_color = cv2.cvtColor(np.uint8([[[b, g, r]]]), cv2.COLOR_BGR2HSV)[0][0]
        
        return (r, g, b), hsv_color
    
    def is_light_green_card(self, rgb_color, hsv_color):
        """Check if the color matches light green card characteristics"""
        if rgb_color is None or hsv_color is None:
            return False, 0
        
        h, s, v = hsv_color
        
        # Check if it's in the green hue range
        hue_match = self.target_hue_range[0] <= h <= self.target_hue_range[1]
        
        # Check saturation (light green should have moderate saturation)
        sat_match = self.target_saturation_range[0] <= s <= self.target_saturation_range[1]
        
        # Check value (should be bright)
        val_match = self.target_value_range[0] <= v <= self.target_value_range[1]
        
        # Calculate confidence score
        confidence = 0
        if hue_match:
            confidence += 40
        if sat_match:
            confidence += 30
        if val_match:
            confidence += 30
        
        # Bonus for being close to ideal light green
        ideal_distance = abs(h - 80) + abs(s - 50) + abs(v - 200)
        if ideal_distance < 50:
            confidence += 20
        
        return hue_match and sat_match and val_match, min(confidence, 100)
    
    def create_color_mask(self, image):
        """Create a mask for light green colors"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Create mask for light green colors
        lower_green = np.array([self.target_hue_range[0], self.target_saturation_range[0], self.target_value_range[0]])
        upper_green = np.array([self.target_hue_range[1], self.target_saturation_range[1], self.target_value_range[1]])
        
        mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # Clean up the mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        return mask
    
    def load_test_image(self, test_num):
        """Load a test image from the test images folder"""
        file_path = f"test images/test{test_num}.jpg"
        try:
            self.original_image = cv2.imread(file_path)
            if self.original_image is None:
                messagebox.showerror("Error", f"Could not load test image {test_num}")
                return
            
            self.image = self.original_image.copy()
            self.detections = []
            self.current_detection_index = 0
            self.update_ui_state()
            self.display_image_on_canvas(self.original_image)
            
            # Auto-detect
            self.detect_cards()
                
        except Exception as e:
            messagebox.showerror("Error", f"Error loading test image {test_num}: {str(e)}")
    
    def test_all_images(self):
        """Test all images and report results"""
        results = []
        for i in range(1, 9):
            file_path = f"test images/test{i}.jpg"
            try:
                test_image = cv2.imread(file_path)
                if test_image is None:
                    results.append(f"Test {i}: FAILED - Could not load image")
                    continue
                
                # Temporarily set the image
                original_img = self.original_image
                self.original_image = test_image
                
                # Try detection
                self.detect_cards()
                
                # Check results
                if self.detections:
                    # Check if any detection has good color match
                    good_detections = 0
                    for detection in self.detections:
                        if 'color_confidence' in detection and detection['color_confidence'] > 60:
                            good_detections += 1
                    
                    if good_detections > 0:
                        results.append(f"Test {i}: PASSED - {good_detections} good card(s) detected")
                    else:
                        results.append(f"Test {i}: PARTIAL - {len(self.detections)} detection(s) but poor color match")
                else:
                    results.append(f"Test {i}: FAILED - No detections")
                
                # Restore original image
                self.original_image = original_img
                
            except Exception as e:
                results.append(f"Test {i}: ERROR - {str(e)}")
        
        # Show results
        result_text = "TEST RESULTS:\n\n" + "\n".join(results)
        messagebox.showinfo("Test Results", result_text)
        
        # Print to console for debugging
        print("\n" + "="*50)
        print("AUTOMATED TEST RESULTS")
        print("="*50)
        for result in results:
            print(result)
        print("="*50)
    
    def select_image(self):
        file_path = filedialog.askopenfilename(
            title="Select an image",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.original_image = cv2.imread(file_path)
                if self.original_image is None:
                    messagebox.showerror("Error", "Could not load the image file.")
                    return
                
                self.image = self.original_image.copy()
                self.detections = []
                self.current_detection_index = 0
                self.update_ui_state()
                self.display_image_on_canvas(self.original_image)
                
            except Exception as e:
                messagebox.showerror("Error", f"Error loading image: {str(e)}")

    def detect_cards(self):
        if self.original_image is None:
            messagebox.showwarning("Warning", "Please select an image first.")
            return

        method = self.method_var.get()

        if method == "Color Separation Enhanced":
            self.detect_with_color_separation_enhanced()
        elif method == "Phone Image Optimized":
            self.detect_with_phone_optimization()
        elif method == "Carpet Background":
            self.detect_with_carpet_background()
        elif method == "Multi-Method":
            self.detect_with_multi_method()

        self.current_detection_index = 0
        self.update_ui_state()
        self.update_display()

    def detect_with_color_separation_enhanced(self):
        """Enhanced color separation with light green card filtering"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
            contrast = float(self.contrast_var.get())
            color_threshold = float(self.color_threshold_var.get())
            use_color_filter = self.use_color_filter_var.get()
        except ValueError:
            min_area, min_aspect, contrast, color_threshold = 3000, 0.4, 2.5, 0.3
            use_color_filter = True

        self.preprocessing_steps = []

        # Step 1: Color enhancement for better separation
        lab = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        l = clahe.apply(l)

        # Enhance A and B channels for better color separation
        a = cv2.convertScaleAbs(a, alpha=1.3, beta=15)
        b = cv2.convertScaleAbs(b, alpha=1.3, beta=15)

        enhanced_lab = cv2.merge([l, a, b])
        color_enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        self.preprocessing_steps.append(("1. Color Enhanced", color_enhanced.copy()))

        # Step 2: Create color mask for light green if enabled
        color_mask = None
        if use_color_filter:
            color_mask = self.create_color_mask(color_enhanced)
            self.preprocessing_steps.append(("2. Color Mask", color_mask.copy()))

        # Step 3: Convert to grayscale using L channel (best contrast)
        gray = l  # Use enhanced L channel
        self.preprocessing_steps.append(("3. L-Channel Gray", gray.copy()))

        # Step 4: Aggressive contrast enhancement
        enhanced = cv2.convertScaleAbs(gray, alpha=contrast, beta=30)
        self.preprocessing_steps.append(("4. High Contrast", enhanced.copy()))

        # Step 5: Edge detection with multiple thresholds
        edges1 = cv2.Canny(enhanced, 20, 60)
        edges2 = cv2.Canny(enhanced, 40, 120)
        edges3 = cv2.Canny(enhanced, 80, 200)

        combined_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))
        self.preprocessing_steps.append(("5. Multi-Threshold Edges", combined_edges.copy()))

        # Step 6: Morphological operations
        kernel_small = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))

        closed = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel_small)
        dilated = cv2.dilate(closed, kernel_small, iterations=2)
        final_edges = cv2.morphologyEx(dilated, cv2.MORPH_CLOSE, kernel_large)

        self.preprocessing_steps.append(("6. Final Edges", final_edges.copy()))
        self.processed_image = final_edges

        # Find contours
        contours, _ = cv2.findContours(final_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            # Get dominant color in the region
            rgb_color, hsv_color = self.get_dominant_color_in_region(color_enhanced, contour)

            # Check if it matches light green card color
            is_card_color, color_confidence = self.is_light_green_card(rgb_color, hsv_color)

            # Apply color filter if enabled
            if use_color_filter and color_confidence < 40:
                continue

            # Additional shape filtering
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                continue

            # Rectangularity check
            rect_area = w * h
            extent = float(area) / rect_area if rect_area != 0 else 0

            if extent < 0.5:  # Too irregular
                continue

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'rgb_color': rgb_color,
                'hsv_color': hsv_color,
                'color_confidence': color_confidence,
                'is_card_color': is_card_color,
                'extent': extent,
                'method': 'Color Separation Enhanced'
            }
            self.detections.append(detection)

        # Sort by color confidence first, then by area
        self.detections.sort(key=lambda x: (x['color_confidence'], x['area']), reverse=True)

    def detect_with_phone_optimization(self):
        """Optimized for phone camera images with varying lighting"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
            use_color_filter = self.use_color_filter_var.get()
        except ValueError:
            min_area, min_aspect = 3000, 0.4
            use_color_filter = True

        self.preprocessing_steps = []

        # Step 1: Correct for phone camera distortions
        # Apply bilateral filter to reduce noise while preserving edges
        denoised = cv2.bilateralFilter(self.original_image, 9, 75, 75)
        self.preprocessing_steps.append(("1. Denoised", denoised.copy()))

        # Step 2: Adaptive histogram equalization in LAB space
        lab = cv2.cvtColor(denoised, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        # Multi-scale CLAHE for varying lighting conditions
        clahe_small = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(4,4))
        clahe_large = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(16,16))

        l_small = clahe_small.apply(l)
        l_large = clahe_large.apply(l)
        l_combined = cv2.addWeighted(l_small, 0.6, l_large, 0.4, 0)

        enhanced_lab = cv2.merge([l_combined, a, b])
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        self.preprocessing_steps.append(("2. Adaptive Enhancement", enhanced.copy()))

        # Step 3: Color mask for light green
        color_mask = None
        if use_color_filter:
            color_mask = self.create_color_mask(enhanced)
            # Dilate color mask to be more inclusive
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            color_mask = cv2.dilate(color_mask, kernel, iterations=2)
            self.preprocessing_steps.append(("3. Dilated Color Mask", color_mask.copy()))

        # Step 4: Edge detection on enhanced L channel
        gray = l_combined

        # Multiple edge detection approaches
        edges1 = cv2.Canny(gray, 30, 90)

        # Sobel edges for better texture detection
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        sobel_combined = np.sqrt(sobelx**2 + sobely**2)
        sobel_edges = np.uint8(sobel_combined / sobel_combined.max() * 255)

        # Combine edges
        combined_edges = cv2.bitwise_or(edges1, sobel_edges)
        self.preprocessing_steps.append(("4. Combined Edges", combined_edges.copy()))

        # Step 5: Apply color mask to edges if enabled
        if use_color_filter and color_mask is not None:
            combined_edges = cv2.bitwise_and(combined_edges, color_mask)
            self.preprocessing_steps.append(("5. Color-Filtered Edges", combined_edges.copy()))

        # Step 6: Morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        closed = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel)

        # Fill holes for phone image artifacts
        kernel_fill = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (8, 8))
        filled = cv2.morphologyEx(closed, cv2.MORPH_CLOSE, kernel_fill)

        self.preprocessing_steps.append(("6. Filled Edges", filled.copy()))
        self.processed_image = filled

        # Find contours
        contours, _ = cv2.findContours(filled, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            # Get dominant color
            rgb_color, hsv_color = self.get_dominant_color_in_region(enhanced, contour)
            is_card_color, color_confidence = self.is_light_green_card(rgb_color, hsv_color)

            # More lenient color filtering for phone images
            if use_color_filter and color_confidence < 30:
                continue

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'rgb_color': rgb_color,
                'hsv_color': hsv_color,
                'color_confidence': color_confidence,
                'is_card_color': is_card_color,
                'method': 'Phone Image Optimized'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: (x['color_confidence'], x['area']), reverse=True)

    def detect_with_carpet_background(self):
        """Specialized for detecting cards on carpet/textured backgrounds"""
        try:
            min_area = float(self.min_area_var.get())
            min_aspect = float(self.aspect_ratio_var.get())
            use_color_filter = self.use_color_filter_var.get()
        except ValueError:
            min_area, min_aspect = 3000, 0.4
            use_color_filter = True

        self.preprocessing_steps = []

        # Step 1: Aggressive smoothing to remove carpet texture
        # Use large bilateral filter to smooth texture while preserving card edges
        smooth1 = cv2.bilateralFilter(self.original_image, 15, 100, 100)
        smooth2 = cv2.bilateralFilter(smooth1, 15, 100, 100)
        self.preprocessing_steps.append(("1. Texture Smoothed", smooth2.copy()))

        # Step 2: Color space conversion and enhancement
        lab = cv2.cvtColor(smooth2, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        # Extreme CLAHE for carpet contrast
        clahe = cv2.createCLAHE(clipLimit=6.0, tileGridSize=(6,6))
        l_enhanced = clahe.apply(l)

        enhanced_lab = cv2.merge([l_enhanced, a, b])
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        self.preprocessing_steps.append(("2. Extreme CLAHE", enhanced.copy()))

        # Step 3: Color mask for light green (more aggressive)
        color_mask = None
        if use_color_filter:
            color_mask = self.create_color_mask(enhanced)
            # More aggressive dilation for carpet
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (10, 10))
            color_mask = cv2.dilate(color_mask, kernel, iterations=3)
            self.preprocessing_steps.append(("3. Aggressive Color Mask", color_mask.copy()))

        # Step 4: Multiple edge detection approaches
        gray = l_enhanced

        # High contrast enhancement
        contrast_enhanced = cv2.convertScaleAbs(gray, alpha=3.5, beta=50)

        # Multiple Canny with very low thresholds for carpet
        edges1 = cv2.Canny(contrast_enhanced, 10, 30)
        edges2 = cv2.Canny(contrast_enhanced, 20, 60)
        edges3 = cv2.Canny(contrast_enhanced, 40, 120)

        # Laplacian for texture differences
        laplacian = cv2.Laplacian(contrast_enhanced, cv2.CV_64F)
        laplacian_edges = np.uint8(np.absolute(laplacian))

        # Combine all edges
        all_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, cv2.bitwise_or(edges3, laplacian_edges)))
        self.preprocessing_steps.append(("4. Multi-Edge Detection", all_edges.copy()))

        # Step 5: Apply color mask to focus on card areas
        if use_color_filter and color_mask is not None:
            all_edges = cv2.bitwise_and(all_edges, color_mask)
            self.preprocessing_steps.append(("5. Color-Focused Edges", all_edges.copy()))

        # Step 6: Aggressive morphological operations for carpet
        kernel_small = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        kernel_large = cv2.getStructuringElement(cv2.MORPH_RECT, (12, 12))

        # Close small gaps
        closed = cv2.morphologyEx(all_edges, cv2.MORPH_CLOSE, kernel_small)
        # Heavy dilation to connect card edges
        dilated = cv2.dilate(closed, kernel_small, iterations=4)
        # Close large gaps typical in carpet images
        final = cv2.morphologyEx(dilated, cv2.MORPH_CLOSE, kernel_large)

        self.preprocessing_steps.append(("6. Carpet Morphology", final.copy()))
        self.processed_image = final

        # Find contours
        contours, _ = cv2.findContours(final, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        self.detections = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = min(w, h) / max(w, h)

            if aspect_ratio < min_aspect:
                continue

            # Get dominant color
            rgb_color, hsv_color = self.get_dominant_color_in_region(enhanced, contour)
            is_card_color, color_confidence = self.is_light_green_card(rgb_color, hsv_color)

            # Very lenient color filtering for carpet backgrounds
            if use_color_filter and color_confidence < 25:
                continue

            detection = {
                'contour': contour,
                'bbox': (x, y, w, h),
                'area': area,
                'aspect_ratio': aspect_ratio,
                'rgb_color': rgb_color,
                'hsv_color': hsv_color,
                'color_confidence': color_confidence,
                'is_card_color': is_card_color,
                'method': 'Carpet Background'
            }
            self.detections.append(detection)

        self.detections.sort(key=lambda x: (x['color_confidence'], x['area']), reverse=True)

    def detect_with_multi_method(self):
        """Combine results from multiple methods"""
        # Store original detections
        original_detections = self.detections.copy()

        # Try all methods
        all_detections = []

        # Method 1: Color Separation Enhanced
        self.detect_with_color_separation_enhanced()
        for det in self.detections:
            det['source_method'] = 'Color Separation'
            all_detections.append(det)

        # Method 2: Phone Optimized
        self.detect_with_phone_optimization()
        for det in self.detections:
            det['source_method'] = 'Phone Optimized'
            all_detections.append(det)

        # Method 3: Carpet Background
        self.detect_with_carpet_background()
        for det in self.detections:
            det['source_method'] = 'Carpet Background'
            all_detections.append(det)

        # Deduplicate based on overlap
        final_detections = []
        for det in all_detections:
            is_duplicate = False
            x1, y1, w1, h1 = det['bbox']

            for existing in final_detections:
                x2, y2, w2, h2 = existing['bbox']

                # Calculate overlap
                overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                overlap_area = overlap_x * overlap_y

                area1 = w1 * h1
                area2 = w2 * h2

                # If overlap is more than 50% of either area, it's a duplicate
                if overlap_area > 0.5 * min(area1, area2):
                    # Keep the one with higher color confidence
                    if det['color_confidence'] > existing['color_confidence']:
                        final_detections.remove(existing)
                        final_detections.append(det)
                    is_duplicate = True
                    break

            if not is_duplicate:
                final_detections.append(det)

        # Update method name for combined results
        for det in final_detections:
            det['method'] = f"Multi-Method ({det['source_method']})"

        self.detections = final_detections
        self.detections.sort(key=lambda x: (x['color_confidence'], x['area']), reverse=True)

    def prev_detection(self):
        if self.detections and self.current_detection_index > 0:
            self.current_detection_index -= 1
            self.update_counter_label()
            self.update_display()

    def next_detection(self):
        if self.detections and self.current_detection_index < len(self.detections) - 1:
            self.current_detection_index += 1
            self.update_counter_label()
            self.update_display()

    def update_ui_state(self):
        if self.detections:
            self.prev_btn.config(state=tk.NORMAL)
            self.next_btn.config(state=tk.NORMAL)
            self.crop_btn.config(state=tk.NORMAL)
            self.update_counter_label()
        else:
            self.prev_btn.config(state=tk.DISABLED)
            self.next_btn.config(state=tk.DISABLED)
            self.crop_btn.config(state=tk.DISABLED)
            self.counter_label.config(text="No detections")

    def update_counter_label(self):
        if self.detections:
            self.counter_label.config(
                text=f"Detection {self.current_detection_index + 1} of {len(self.detections)}"
            )

    def update_display(self):
        if self.original_image is None:
            return

        # Choose what to display
        if self.show_preprocessing_var.get() and self.preprocessing_steps:
            self.display_preprocessing_steps()
            return
        elif self.show_color_mask_var.get():
            self.display_color_mask()
            return

        # Show original image with detections
        display_img = self.original_image.copy()

        if not self.detections:
            self.display_image_on_canvas(display_img)
            self.update_info_panel(None)
            self.update_color_display(None)
            return

        # Draw all detections if enabled
        if self.show_all_detections_var.get():
            for i, detection in enumerate(self.detections):
                color = (128, 128, 128) if i != self.current_detection_index else (0, 0, 255)
                thickness = 1 if i != self.current_detection_index else 4
                cv2.drawContours(display_img, [detection['contour']], -1, color, thickness)

        # Highlight current detection
        if self.detections:
            current_detection = self.detections[self.current_detection_index]
            cv2.drawContours(display_img, [current_detection['contour']], -1, (0, 0, 255), 4)

            # Draw bounding rectangle
            x, y, w, h = current_detection['bbox']
            cv2.rectangle(display_img, (x, y), (x + w, y + h), (255, 0, 0), 3)

            # Draw detection info
            confidence = current_detection.get('color_confidence', 0)
            cv2.putText(display_img, f"#{self.current_detection_index + 1} ({confidence:.0f}%)",
                       (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 3)

        self.display_image_on_canvas(display_img)
        self.update_info_panel(current_detection if self.detections else None)
        self.update_color_display(current_detection if self.detections else None)

    def display_color_mask(self):
        """Display the color mask for light green detection"""
        if self.original_image is None:
            return

        color_mask = self.create_color_mask(self.original_image)

        # Convert mask to 3-channel for display
        mask_colored = cv2.cvtColor(color_mask, cv2.COLOR_GRAY2BGR)

        # Overlay on original image
        overlay = cv2.addWeighted(self.original_image, 0.7, mask_colored, 0.3, 0)

        self.display_image_on_canvas(overlay)

    def display_preprocessing_steps(self):
        """Display preprocessing steps in a grid"""
        if not self.preprocessing_steps:
            return

        # Create a grid of preprocessing steps
        num_steps = len(self.preprocessing_steps)
        cols = min(3, num_steps)
        rows = (num_steps + cols - 1) // cols

        # Calculate size for each step image
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, self.display_preprocessing_steps)
            return

        step_width = canvas_width // cols
        step_height = canvas_height // rows

        # Create combined image
        combined_img = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)

        for i, (title, img) in enumerate(self.preprocessing_steps):
            row = i // cols
            col = i % cols

            # Resize step image to fit
            if len(img.shape) == 2:  # Grayscale
                img_resized = cv2.resize(img, (step_width-10, step_height-30))
                img_colored = cv2.cvtColor(img_resized, cv2.COLOR_GRAY2BGR)
            else:
                img_colored = cv2.resize(img, (step_width-10, step_height-30))

            # Calculate position
            y_start = row * step_height + 20
            y_end = y_start + img_colored.shape[0]
            x_start = col * step_width + 5
            x_end = x_start + img_colored.shape[1]

            # Place image
            combined_img[y_start:y_end, x_start:x_end] = img_colored

            # Add title
            cv2.putText(combined_img, title, (x_start, row * step_height + 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        self.display_image_on_canvas(combined_img)

    def display_image_on_canvas(self, img):
        # Convert BGR to RGB
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Get canvas dimensions
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, lambda: self.display_image_on_canvas(img))
            return

        # Calculate scaling factor to fit image in canvas
        img_height, img_width = img_rgb.shape[:2]
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # Don't upscale

        # Resize image
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        img_resized = cv2.resize(img_rgb, (new_width, new_height))

        # Convert to PIL Image and then to PhotoImage
        pil_img = Image.fromarray(img_resized)
        self.display_image = ImageTk.PhotoImage(pil_img)

        # Clear canvas and display image
        self.canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        self.canvas.create_image(x, y, anchor=tk.NW, image=self.display_image)

    def update_color_display(self, detection):
        """Update the color display panel"""
        self.color_canvas.delete("all")

        if detection is None or 'rgb_color' not in detection or detection['rgb_color'] is None:
            self.color_canvas.configure(bg="white")
            self.color_info_label.config(text="No color detected")
            return

        r, g, b = detection['rgb_color']
        hex_color = self.rgb_to_hex(r, g, b)

        # Display color
        self.color_canvas.configure(bg=hex_color)

        # Create color info text
        h, s, v = detection['hsv_color']
        confidence = detection.get('color_confidence', 0)

        color_info = f"RGB: ({r}, {g}, {b})\n"
        color_info += f"HEX: {hex_color}\n"
        color_info += f"HSV: ({h}, {s}, {v})\n"
        color_info += f"Confidence: {confidence:.1f}%"

        self.color_info_label.config(text=color_info)

    def update_info_panel(self, detection):
        self.info_text.delete(1.0, tk.END)

        if detection is None:
            info = "No detection selected.\n\n"
            info += "LIGHT GREEN CARD DETECTION:\n"
            info += f"Target Hue: {self.target_hue_range[0]}-{self.target_hue_range[1]} (Green)\n"
            info += f"Target Saturation: {self.target_saturation_range[0]}-{self.target_saturation_range[1]} (Light)\n"
            info += f"Target Value: {self.target_value_range[0]}-{self.target_value_range[1]} (Bright)\n\n"
            info += "METHODS AVAILABLE:\n"
            info += "• Color Separation Enhanced: Best for clear images\n"
            info += "• Phone Image Optimized: For phone camera images\n"
            info += "• Carpet Background: For textured backgrounds\n"
            info += "• Multi-Method: Combines all approaches\n\n"
            info += "Use 'Test All' to check all test images automatically."
            self.info_text.insert(tk.END, info)
            return

        # Calculate additional properties
        contour = detection['contour']
        area = detection['area']
        x, y, w, h = detection['bbox']

        info = f"""DETECTION #{self.current_detection_index + 1}
Method: {detection['method']}
Rank: {self.current_detection_index + 1} (by color confidence)

BASIC PROPERTIES:
Area: {area:.0f} pixels
Position: ({x}, {y})
Size: {w} × {h} pixels
Aspect Ratio: {detection['aspect_ratio']:.3f}

COLOR ANALYSIS:"""

        if 'rgb_color' in detection and detection['rgb_color'] is not None:
            r, g, b = detection['rgb_color']
            h, s, v = detection['hsv_color']
            hex_color = self.rgb_to_hex(r, g, b)

            info += f"""
RGB Color: ({r}, {g}, {b})
HEX Color: {hex_color}
HSV Color: ({h}, {s}, {v})
Color Confidence: {detection['color_confidence']:.1f}%
Is Card Color: {'✓ YES' if detection['is_card_color'] else '✗ NO'}"""

        # Quality assessment
        confidence = detection.get('color_confidence', 0)

        info += f"\n\nQUALITY ASSESSMENT:"
        if confidence >= 80:
            info += f"\n🟢 EXCELLENT match ({confidence:.1f}%)"
        elif confidence >= 60:
            info += f"\n🟡 GOOD match ({confidence:.1f}%)"
        elif confidence >= 40:
            info += f"\n🟠 FAIR match ({confidence:.1f}%)"
        else:
            info += f"\n🔴 POOR match ({confidence:.1f}%)"

        # Shape quality
        if 'extent' in detection:
            extent = detection['extent']
            info += f"\nRectangularity: {extent:.3f}"
            if extent > 0.7:
                info += " ✓ Good"
            else:
                info += " ✗ Poor"

        # Method-specific info
        info += f"\n\nMETHOD DETAILS:"
        if "Color Separation" in detection['method']:
            info += "\n• LAB color space optimization"
            info += "\n• Multi-threshold edge detection"
            info += "\n• Color-based filtering"
        elif "Phone Image" in detection['method']:
            info += "\n• Bilateral noise reduction"
            info += "\n• Multi-scale CLAHE"
            info += "\n• Sobel + Canny edges"
        elif "Carpet Background" in detection['method']:
            info += "\n• Aggressive texture smoothing"
            info += "\n• Extreme contrast enhancement"
            info += "\n• Heavy morphological operations"
        elif "Multi-Method" in detection['method']:
            info += "\n• Combined multiple approaches"
            info += "\n• Deduplication applied"
            info += f"\n• Source: {detection.get('source_method', 'Unknown')}"

        self.info_text.insert(tk.END, info)

    def crop_current(self):
        if not self.detections or self.original_image is None:
            return

        detection = self.detections[self.current_detection_index]
        x, y, w, h = detection['bbox']

        # Add some padding
        padding = 15
        x = max(0, x - padding)
        y = max(0, y - padding)
        w = min(self.original_image.shape[1] - x, w + 2 * padding)
        h = min(self.original_image.shape[0] - y, h + 2 * padding)

        # Crop the image
        cropped = self.original_image[y:y+h, x:x+w]

        # Generate default filename with color info
        if 'rgb_color' in detection and detection['rgb_color'] is not None:
            r, g, b = detection['rgb_color']
            hex_color = self.rgb_to_hex(r, g, b)
            default_name = f"card_{hex_color}_{detection['color_confidence']:.0f}pct.jpg"
        else:
            default_name = f"card_detection_{self.current_detection_index + 1}.jpg"

        # Save the cropped image
        save_path = filedialog.asksaveasfilename(
            title="Save cropped card",
            initialvalue=default_name,
            defaultextension=".jpg",
            filetypes=[
                ("JPEG files", "*.jpg"),
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
        )

        if save_path:
            cv2.imwrite(save_path, cropped)
            confidence = detection.get('color_confidence', 0)
            messagebox.showinfo("Success",
                f"Card cropped and saved!\n\nFile: {save_path}\nColor Confidence: {confidence:.1f}%")

def main():
    root = tk.Tk()
    app = ColorAwareCardDetector(root)
    root.mainloop()

if __name__ == "__main__":
    main()
